#!/usr/bin/env python3
"""
Simple HTTP server with API endpoints for the SVG Icon Library Admin Panel
"""

import json
import os
from http.server import HTTPServer, SimpleHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import cgi

class IconLibraryHandler(SimpleHTTPRequestHandler):
    def do_POST(self):
        """Handle POST requests for saving icon data"""
        parsed_path = urlparse(self.path)
        
        if parsed_path.path == '/api/save-icons':
            self.handle_save_icons()
        else:
            self.send_error(404, "Not Found")
    
    def handle_save_icons(self):
        """Save icons data to JSON file"""
        try:
            # Get content length
            content_length = int(self.headers['Content-Length'])
            
            # Read the POST data
            post_data = self.rfile.read(content_length)
            
            # Parse JSON data
            icons_data = json.loads(post_data.decode('utf-8'))
            
            # Validate data structure
            if not isinstance(icons_data, list):
                raise ValueError("Data must be an array of icons")
            
            # Validate each icon
            for icon in icons_data:
                required_fields = ['id', 'name', 'category', 'svg']
                for field in required_fields:
                    if field not in icon:
                        raise ValueError(f"Missing required field: {field}")
            
            # Create backup of existing file
            if os.path.exists('icons-data.json'):
                import shutil
                import datetime
                timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
                backup_name = f'icons-data-backup-{timestamp}.json'
                shutil.copy2('icons-data.json', backup_name)
                print(f"Created backup: {backup_name}")
            
            # Save new data
            with open('icons-data.json', 'w', encoding='utf-8') as f:
                json.dump(icons_data, f, indent=2, ensure_ascii=False)
            
            # Send success response
            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            
            response = {
                'success': True,
                'message': f'Successfully saved {len(icons_data)} icons',
                'count': len(icons_data)
            }
            self.wfile.write(json.dumps(response).encode('utf-8'))
            
            print(f"Successfully saved {len(icons_data)} icons to icons-data.json")
            
        except Exception as e:
            print(f"Error saving icons: {e}")
            self.send_error(500, f"Error saving icons: {str(e)}")
    
    def do_OPTIONS(self):
        """Handle CORS preflight requests"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
    
    def end_headers(self):
        """Add CORS headers to all responses"""
        self.send_header('Access-Control-Allow-Origin', '*')
        super().end_headers()

def run_server(port=8000):
    """Run the HTTP server"""
    server_address = ('', port)
    httpd = HTTPServer(server_address, IconLibraryHandler)
    
    print(f"Starting SVG Icon Library server on port {port}")
    print(f"Main app: http://localhost:{port}/")
    print(f"Admin panel: http://localhost:{port}/admin.html")
    print("Press Ctrl+C to stop the server")
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\nServer stopped.")
        httpd.server_close()

if __name__ == '__main__':
    import sys
    
    port = 8000
    if len(sys.argv) > 1:
        try:
            port = int(sys.argv[1])
        except ValueError:
            print("Invalid port number. Using default port 8000.")
    
    run_server(port)
