# SVG Icon Library

A comprehensive web application featuring 100 unique, minimalistic SVG icons suitable for desktop, web, and mobile applications.

## Features

### 🎨 **Icon Collection**
- **100 unique SVG icons** across 6 categories:
  - **Navigation** (20 icons): arrows, menu, home, compass, etc.
  - **Actions** (20 icons): save, edit, delete, search, copy, etc.
  - **Communication** (20 icons): email, phone, chat, notifications, etc.
  - **Media** (20 icons): play, pause, volume, camera, video, etc.
  - **Files** (20 icons): folder, document, download, cloud, etc.
  - **UI Elements** (20 icons): settings, user, lock, heart, star, etc.

### 🎛️ **Interactive Controls**
- **Color Customization**: Real-time color picker with preset palette
- **Size Control**: Adjustable icon size from 16px to 128px
- **Search & Filter**: Find icons by name or category
- **Preview Area**: View selected icons at multiple sizes

### 📋 **Export Features**
- **Copy SVG**: One-click copy to clipboard with user feedback
- **Export SVG**: Download individual icons as optimized .svg files
- **Proper Formatting**: All exported SVGs include proper viewBox attributes

### 📱 **Responsive Design**
- Mobile-friendly responsive layout
- Touch-optimized controls
- Adaptive grid system
- Modern, clean interface

## Usage

### Getting Started
1. Open `index.html` in a web browser
2. Browse the icon grid or use search/filter to find specific icons
3. Click on any icon to preview it at different sizes
4. Customize colors using the color picker or presets
5. Adjust display size with the size slider

### Copying Icons
1. Hover over any icon to reveal action buttons
2. Click "Copy" to copy the SVG code to clipboard
3. The SVG will include your current color selection
4. Paste directly into your HTML or design tools

### Exporting Icons
1. Click "Export" on any icon to download as .svg file
2. Files are properly formatted with XML declaration
3. Colors are applied based on your current selection
4. Files are named using the icon ID for easy organization

### Search and Filter
- **Search**: Type in the search box to find icons by name
- **Category Filter**: Use dropdown to filter by category
- **Combined**: Search and category filters work together

## Technical Details

### File Structure
```
svg-icon-library/
├── index.html          # Main HTML structure
├── styles.css          # CSS styling and responsive design
├── app.js             # Main application logic
├── icons-data.js      # SVG icon data structure
└── README.md          # Documentation
```

### Icon Format
All icons are designed with:
- **Consistent stroke width**: 2px for optimal scaling
- **Clean viewBox**: 24x24 for perfect scaling
- **Minimal code**: Optimized SVG paths
- **Scalable design**: Works from 16px to 128px+

### Browser Compatibility
- Modern browsers (Chrome, Firefox, Safari, Edge)
- Mobile browsers (iOS Safari, Chrome Mobile)
- Responsive design for all screen sizes

### Performance
- Lightweight: No external dependencies except Google Fonts
- Fast rendering: Optimized SVG code
- Efficient filtering: Client-side search and categorization
- Smooth interactions: CSS transitions and hover effects

## Customization

### Adding New Icons
1. Add new icon objects to `iconsData` array in `icons-data.js`
2. Follow the existing format:
```javascript
{
    id: 'unique-id',
    name: 'Display Name',
    category: 'category-name',
    svg: '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">...</svg>'
}
```

### Styling
- Modify `styles.css` to customize appearance
- Color scheme can be adjusted in CSS variables
- Grid layout is fully responsive and customizable

### Functionality
- Extend `app.js` to add new features
- All methods are well-documented and modular
- Event handling is centralized for easy modification

## License

This project is open source and available under the MIT License.

## Credits

Icons are designed with clean, minimalistic aesthetics suitable for professional applications. The library uses modern web technologies for optimal performance and user experience.
