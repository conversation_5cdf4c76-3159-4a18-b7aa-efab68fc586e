/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: #f8f9fa;
    color: #333;
    line-height: 1.6;
}

.app-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

/* Header */
.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-align: center;
    margin-bottom: 30px;
    padding: 40px 20px;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.app-title {
    font-size: 2.5rem;
    font-weight: 600;
    color: white;
    margin-bottom: 8px;
}

.app-subtitle {
    font-size: 1.1rem;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 400;
}

/* Controls Panel */
.controls-panel {
    background: white;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 30px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.control-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.control-group label {
    font-weight: 500;
    color: #495057;
    font-size: 0.9rem;
}

.control-group input,
.control-group select {
    padding: 10px 12px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 0.9rem;
    transition: border-color 0.2s ease;
}

.control-group input:focus,
.control-group select:focus {
    outline: none;
    border-color: #007bff;
}

.color-presets {
    display: flex;
    gap: 8px;
    margin-top: 8px;
}

.color-preset {
    width: 32px;
    height: 32px;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    cursor: pointer;
    transition: transform 0.2s ease, border-color 0.2s ease;
}

.color-preset:hover {
    transform: scale(1.1);
    border-color: #007bff;
}

.color-preset.active {
    border-color: #007bff;
    transform: scale(1.1);
}

#size-slider {
    width: 100%;
}

#size-value {
    font-weight: 600;
    color: #007bff;
}

/* Preview Area */
.preview-area {
    background: white;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 30px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.preview-area h3 {
    margin-bottom: 20px;
    color: #2c3e50;
}

.preview-content {
    display: grid;
    grid-template-columns: auto 1fr;
    gap: 30px;
    align-items: start;
}

.preview-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 120px;
    height: 120px;
    background: #f8f9fa;
    border-radius: 12px;
    border: 2px solid #e9ecef;
}

.preview-info h4 {
    font-size: 1.3rem;
    margin-bottom: 8px;
    color: #2c3e50;
}

.preview-info p {
    color: #6c757d;
    margin-bottom: 20px;
    text-transform: capitalize;
}

.preview-sizes {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.size-demo {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
}

.size-demo span {
    font-size: 0.8rem;
    color: #6c757d;
    font-weight: 500;
}

.size-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
}

.size-16 { width: 32px; height: 32px; }
.size-24 { width: 40px; height: 40px; }
.size-32 { width: 48px; height: 48px; }
.size-48 { width: 64px; height: 64px; }
.size-64 { width: 80px; height: 80px; }

/* Icons Grid */
.icons-container {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.icons-grid {
    display: grid !important;
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr)) !important;
    gap: 20px !important;
    width: 100%;
}

.icon-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
    border: 2px solid #f1f3f4;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    background: #fafbfc;
}

.icon-item:hover {
    border-color: #007bff;
    background: #f8f9ff;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);
}

.icon-item.selected {
    border-color: #007bff;
    background: #e3f2fd;
}

.icon-display {
    /* Size will be set dynamically by JavaScript */
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 12px;
}

.icon-display svg {
    width: 100%;
    height: 100%;
    max-width: 100%;
    max-height: 100%;
}

.icon-name {
    font-size: 0.9rem;
    font-weight: 500;
    color: #2c3e50;
    text-align: center;
    margin-bottom: 8px;
}

.icon-category {
    font-size: 0.8rem;
    color: #6c757d;
    text-transform: capitalize;
    margin-bottom: 12px;
}

.icon-actions {
    display: flex;
    gap: 8px;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.icon-item:hover .icon-actions {
    opacity: 1;
}

.action-btn {
    padding: 6px 12px;
    font-size: 0.8rem;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.2s ease;
}

.copy-btn {
    background: #007bff;
    color: white;
}

.copy-btn:hover {
    background: #0056b3;
}

.export-btn {
    background: #28a745;
    color: white;
}

.export-btn:hover {
    background: #1e7e34;
}

/* Notification */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #28a745;
    color: white;
    padding: 12px 20px;
    border-radius: 8px;
    font-weight: 500;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    z-index: 1000;
}

.notification.show {
    transform: translateX(0);
}

/* Cookie Consent */
.cookie-consent {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: #2c3e50;
    color: white;
    padding: 15px 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    z-index: 1000;
    display: none;
}

.cookie-consent p {
    margin: 0;
    font-size: 0.9rem;
}

.cookie-consent a {
    color: #007bff;
    text-decoration: none;
}

.cookie-consent button {
    background: #007bff;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    margin-left: 15px;
}

/* Footer */
.font-license {
    text-align: center;
    font-size: 0.8rem;
    color: #6c757d;
    margin-bottom: 15px;
}

.site-footer {
    text-align: center;
    padding: 30px 0;
    margin-top: 50px;
    color: #6c757d;
    font-size: 0.9rem;
    border-top: 1px solid #e9ecef;
}

.site-footer a {
    color: #007bff;
    text-decoration: none;
}

.site-footer p {
    margin-bottom: 10px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .app-container {
        padding: 15px;
    }
    
    .app-title {
        font-size: 2rem;
    }
    
    .controls-panel {
        grid-template-columns: 1fr;
        padding: 20px;
    }
    
    .preview-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .preview-sizes {
        justify-content: center;
    }
    
    .icons-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 15px;
    }
    
    .icon-item {
        padding: 15px;
    }
}

@media (max-width: 480px) {
    .icons-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: 12px;
    }
    
    .color-presets {
        justify-content: center;
    }
}
