// SVG Icons Data will be loaded from JSON file
let iconsData = [];

// Function to load icons data from JSON file
async function loadIconsData() {
    try {
        const response = await fetch('icons-data.json');
        if (!response.ok) {
            throw new Error('Failed to load icons data');
        }
        iconsData = await response.json();
        return iconsData;
    } catch (error) {
        console.error('Error loading icons data:', error);
        return [];
    }
}

// SVG Icon Library Application
class IconLibrary {
    constructor() {
        this.currentColor = '#333333';
        this.currentSize = 32;
        this.filteredIcons = [];
        this.selectedIcon = null;
        this.init();
    }

    async init() {
        // Load icons data first
        await loadIconsData();
        this.filteredIcons = [...iconsData];
        
        this.setupEventListeners();
        this.renderIcons();
        this.updateSizeDisplay();
    }

    setupEventListeners() {
        // Search functionality
        const searchInput = document.getElementById('search-input');
        searchInput.addEventListener('input', (e) => {
            this.filterIcons(e.target.value, document.getElementById('category-filter').value);
        });
        
        // Category filter
        const categoryFilter = document.getElementById('category-filter');
        categoryFilter.addEventListener('change', (e) => {
            this.filterIcons(document.getElementById('search-input').value, e.target.value);
        });
        
        // Color picker
        const colorPicker = document.getElementById('color-picker');
        colorPicker.addEventListener('input', (e) => {
            this.updateColor(e.target.value);
        });
        
        // Color presets
        const colorPresets = document.querySelectorAll('.color-preset');
        colorPresets.forEach(preset => {
            preset.addEventListener('click', (e) => {
                const color = e.target.dataset.color;
                this.updateColor(color);
                document.getElementById('color-picker').value = color;
                
                // Update active preset
                colorPresets.forEach(p => p.classList.remove('active'));
                e.target.classList.add('active');
            });
        });
        
        // Size slider
        const sizeSlider = document.getElementById('size-slider');
        sizeSlider.addEventListener('input', (e) => {
            this.updateSize(parseInt(e.target.value));
        });
    }
    
    filterIcons(searchTerm, category) {
        this.filteredIcons = iconsData.filter(icon => {
            const matchesSearch = !searchTerm || 
                icon.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                icon.category.toLowerCase().includes(searchTerm.toLowerCase());
            
            const matchesCategory = category === 'all' || icon.category === category;
            
            return matchesSearch && matchesCategory;
        });
        
        this.renderIcons();
    }
    
    renderIcons() {
        const iconsGrid = document.getElementById('icons-grid');
        iconsGrid.innerHTML = '';
        
        this.filteredIcons.forEach(icon => {
            const iconElement = this.createIconElement(icon);
            iconsGrid.appendChild(iconElement);
        });
    }
    
    createIconElement(icon) {
        const iconItem = document.createElement('div');
        iconItem.className = 'icon-item';
        iconItem.dataset.iconId = icon.id;
        
        iconItem.innerHTML = `
            <div class="icon-display" style="color: ${this.currentColor}; width: ${this.currentSize}px; height: ${this.currentSize}px;">
                ${icon.svg}
            </div>
            <div class="icon-name">${icon.name}</div>
            <div class="icon-category">${icon.category}</div>
            <div class="icon-actions">
                <button class="action-btn copy-btn" onclick="iconLibrary.copyIcon('${icon.id}')">Copy</button>
                <button class="action-btn export-btn" onclick="iconLibrary.exportIcon('${icon.id}')">Export</button>
            </div>
        `;
        
        // Add click event for preview
        iconItem.addEventListener('click', () => {
            this.selectIcon(icon);
        });
        
        return iconItem;
    }
    
    selectIcon(icon) {
        // Remove previous selection
        document.querySelectorAll('.icon-item').forEach(item => {
            item.classList.remove('selected');
        });
        
        // Add selection to current icon
        const iconElement = document.querySelector(`[data-icon-id="${icon.id}"]`);
        if (iconElement) {
            iconElement.classList.add('selected');
        }
        
        this.selectedIcon = icon;
        this.showPreview(icon);
    }
    
    showPreview(icon) {
        const previewArea = document.getElementById('preview-area');
        const previewIcon = document.getElementById('preview-icon');
        const previewName = document.getElementById('preview-name');
        const previewCategory = document.getElementById('preview-category');
        
        // Show preview area
        previewArea.style.display = 'block';
        
        // Update preview content
        previewIcon.innerHTML = icon.svg;
        previewIcon.style.color = this.currentColor;
        previewName.textContent = icon.name;
        previewCategory.textContent = icon.category;
        
        // Update size previews
        const sizes = [16, 24, 32, 48, 64];
        sizes.forEach(size => {
            const previewElement = document.getElementById(`preview-${size}`);
            previewElement.innerHTML = icon.svg;
            previewElement.style.color = this.currentColor;
        });
    }
    
    updateColor(color) {
        this.currentColor = color;
        
        // Update all displayed icons
        document.querySelectorAll('.icon-display').forEach(display => {
            display.style.color = color;
        });
        
        // Update preview if visible
        if (this.selectedIcon) {
            this.showPreview(this.selectedIcon);
        }
    }
    
    updateSize(size) {
        this.currentSize = size;
        this.updateSizeDisplay();
        
        // Update all displayed icons
        document.querySelectorAll('.icon-display').forEach(display => {
            display.style.width = `${size}px`;
            display.style.height = `${size}px`;
        });
    }
    
    updateSizeDisplay() {
        document.getElementById('size-value').textContent = `${this.currentSize}px`;
    }
    
    copyIcon(iconId) {
        const icon = iconsData.find(i => i.id === iconId);
        if (!icon) return;
        
        const svgWithColor = icon.svg.replace('stroke="currentColor"', `stroke="${this.currentColor}"`);
        
        navigator.clipboard.writeText(svgWithColor).then(() => {
            this.showNotification('SVG copied to clipboard!');
        }).catch(err => {
            console.error('Failed to copy: ', err);
            this.showNotification('Failed to copy SVG', 'error');
        });
    }
    
    exportIcon(iconId) {
        const icon = iconsData.find(i => i.id === iconId);
        if (!icon) return;
        
        const svgWithColor = icon.svg.replace('stroke="currentColor"', `stroke="${this.currentColor}"`);
        const svgContent = `<?xml version="1.0" encoding="UTF-8"?>\n${svgWithColor}`;
        
        const blob = new Blob([svgContent], { type: 'image/svg+xml' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `${icon.id}.svg`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        this.showNotification(`${icon.name} exported successfully!`);
    }
    
    showNotification(message, type = 'success') {
        const notification = document.getElementById('notification');
        const notificationText = document.getElementById('notification-text');
        
        notificationText.textContent = message;
        notification.className = `notification ${type}`;
        notification.classList.add('show');
        
        setTimeout(() => {
            notification.classList.remove('show');
        }, 3000);
    }
}

// Initialize the application
document.addEventListener('DOMContentLoaded', () => {
    window.iconLibrary = new IconLibrary();
});
