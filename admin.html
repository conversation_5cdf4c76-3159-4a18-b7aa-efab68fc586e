<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Panel - SVG Icon Library</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600&display=swap" rel="stylesheet">
    <style>
        .admin-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .admin-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .admin-nav {
            display: flex;
            gap: 10px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }
        
        .nav-btn {
            padding: 12px 24px;
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
            text-decoration: none;
            color: #495057;
        }
        
        .nav-btn:hover, .nav-btn.active {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }
        
        .admin-section {
            background: white;
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            display: none;
        }
        
        .admin-section.active {
            display: block;
        }
        
        .section-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #2c3e50;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 10px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #495057;
        }
        
        .form-group input, .form-group select, .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }
        
        .form-group input:focus, .form-group select:focus, .form-group textarea:focus {
            outline: none;
            border-color: #007bff;
        }
        
        .form-group textarea {
            min-height: 120px;
            resize: vertical;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-primary:hover {
            background: #0056b3;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background: #1e7e34;
        }
        
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        
        .btn-danger:hover {
            background: #c82333;
        }
        
        .btn-secondary {
            background: #2fb350;
            color: white;
            text-decoration: none;
        }
        
        .btn-secondary:hover {
            background: #5bd13e;
        }
        
        .file-upload {
            border: 2px dashed #007bff;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            background: #f8f9ff;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .file-upload:hover {
            background: #e6f3ff;
        }
        
        .file-upload.dragover {
            border-color: #0056b3;
            background: #cce7ff;
        }
        
        .icons-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .icon-card {
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .icon-card:hover {
            border-color: #007bff;
            box-shadow: 0 4px 8px rgba(0, 123, 255, 0.1);
        }
        
        .icon-preview {
            width: 48px;
            height: 48px;
            margin: 0 auto 10px;
            color: #495057;
        }
        
        .icon-info h4 {
            margin: 0 0 5px;
            font-size: 14px;
            font-weight: 600;
        }
        
        .icon-info p {
            margin: 0;
            font-size: 12px;
            color: #6c757d;
        }
        
        .icon-actions {
            margin-top: 10px;
        }
        
        .icon-actions button {
            padding: 6px 12px;
            font-size: 12px;
            margin: 2px;
        }
        
        .categories-list {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 20px;
        }
        
        .category-tag {
            background: #e9ecef;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .category-tag .remove-btn {
            background: #dc3545;
            color: white;
            border: none;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 12px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .login-form {
            max-width: 400px;
            margin: 100px auto;
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
        }
        
        .login-title {
            text-align: center;
            margin-bottom: 30px;
            color: #2c3e50;
        }
        
        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: none;
        }
        
        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: none;
        }
    </style>
</head>
<body>
    <!-- Login Form (shown initially) -->
    <div id="loginForm" class="login-form">
        <h2 class="login-title">Admin Login</h2>
        <div id="errorMessage" class="error-message"></div>
        <form id="adminLoginForm">
            <div class="form-group">
                <label for="adminPassword">Password:</label>
                <input type="password" id="adminPassword" required>
            </div>
            <button type="submit" class="btn btn-primary" style="width: 100%;">Login</button>
        </form>
    </div>

    <!-- Admin Panel (hidden initially) -->
    <div id="adminPanel" class="admin-container" style="display: none;">
        <!-- Breadcrumb Navigation -->
        <nav style="margin-bottom: 20px; padding: 10px 0; border-bottom: 1px solid #e9ecef;">
            <span style="color: #6c757d; font-size: 14px;">
                <a href="index.html" style="color: #007bff; text-decoration: none;">🏠 Main App</a>
                <span style="margin: 0 8px;">›</span>
                <span style="color: #495057;">Admin Panel</span>
                <span style="margin: 0 8px;">›</span>
                <span id="currentSection" style="color: #007bff;">Manage Icons</span>
            </span>
        </nav>

        <div class="admin-header">
            <h1>SVG Icon Library - Admin Panel</h1>
            <p>Manage your icon library with ease</p>
            <div id="adminStats" style="margin: 15px 0; padding: 15px; background: rgba(255,255,255,0.1); border-radius: 8px; font-size: 14px;">
                Loading statistics...
            </div>
            <div style="margin-top: 20px;">
                <a href="index.html" class="btn btn-secondary">← Back to Main App</a>
                <button id="logoutBtn" class="btn btn-danger">Logout</button>
            </div>
        </div>

        <nav class="admin-nav">
            <button class="nav-btn active" data-section="icons">Manage Icons</button>
            <button class="nav-btn" data-section="categories">Manage Categories</button>
            <button class="nav-btn" data-section="import-export">Import/Export</button>
        </nav>

        <!-- Icons Management Section -->
        <section id="icons-section" class="admin-section active">
            <h2 class="section-title">Manage Icons</h2>
            
            <div style="margin-bottom: 30px;">
                <h3>Add New Icon</h3>
                <form id="addIconForm">
                    <div class="form-group">
                        <label for="iconId">Icon ID:</label>
                        <input type="text" id="iconId" placeholder="e.g., my-new-icon" required>
                    </div>
                    <div class="form-group">
                        <label for="iconName">Icon Name:</label>
                        <input type="text" id="iconName" placeholder="e.g., My New Icon" required>
                    </div>
                    <div class="form-group">
                        <label for="iconCategory">Category:</label>
                        <select id="iconCategory" required>
                            <option value="">Select Category</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="iconSvg">SVG Code:</label>
                        <textarea id="iconSvg" placeholder="Paste your SVG code here..." required></textarea>
                        <div id="svgPreview" style="margin-top: 10px; padding: 20px; border: 2px dashed #e9ecef; border-radius: 8px; text-align: center; display: none;">
                            <p style="margin: 0; color: #6c757d;">SVG Preview will appear here</p>
                        </div>
                    </div>
                    <button type="submit" class="btn btn-primary">Add Icon</button>
                    <button type="button" id="previewBtn" class="btn btn-secondary">Preview SVG</button>
                </form>
            </div>

            <div style="margin-bottom: 30px;">
                <h3>Upload SVG Files</h3>
                <div class="file-upload" id="fileUpload">
                    <p>Drag & drop SVG files here or click to select</p>
                    <input type="file" id="fileInput" multiple accept=".svg" style="display: none;">
                </div>
            </div>

            <div>
                <h3>Existing Icons</h3>
                <div id="iconsList" class="icons-list">
                    <!-- Icons will be loaded here -->
                </div>
            </div>
        </section>

        <!-- Categories Management Section -->
        <section id="categories-section" class="admin-section">
            <h2 class="section-title">Manage Categories</h2>
            
            <div style="margin-bottom: 30px;">
                <h3>Add New Category</h3>
                <form id="addCategoryForm">
                    <div class="form-group">
                        <label for="categoryName">Category Name:</label>
                        <input type="text" id="categoryName" placeholder="e.g., social-media" required>
                    </div>
                    <button type="submit" class="btn btn-primary">Add Category</button>
                </form>
            </div>

            <div style="margin-bottom: 30px;">
                <h3>Reassign Icons to Category</h3>
                <form id="reassignCategoryForm">
                    <div class="form-group">
                        <label for="fromCategory">From Category:</label>
                        <select id="fromCategory" required>
                            <option value="">Select source category</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="toCategory">To Category:</label>
                        <select id="toCategory" required>
                            <option value="">Select target category</option>
                        </select>
                    </div>
                    <button type="submit" class="btn btn-primary">Reassign All Icons</button>
                </form>
            </div>

            <div>
                <h3>Existing Categories</h3>
                <div id="categoriesList" class="categories-list">
                    <!-- Categories will be loaded here -->
                </div>
            </div>
        </section>

        <!-- Import/Export Section -->
        <section id="import-export-section" class="admin-section">
            <h2 class="section-title">Import/Export Data</h2>
            
            <div style="margin-bottom: 30px;">
                <h3>Export Icon Library</h3>
                <p>Download the complete icon library data in different formats.</p>
                <button id="exportBtn" class="btn btn-success">Export JSON</button>
                <button id="exportCsvBtn" class="btn btn-success">Export CSV</button>
                <button id="exportBackupBtn" class="btn btn-secondary">Create Backup</button>
            </div>

            <div>
                <h3>Import Icon Library</h3>
                <p>Upload a JSON file to replace or merge with the current icon library.</p>
                <div class="form-group">
                    <label for="importFile">Select JSON File:</label>
                    <input type="file" id="importFile" accept=".json">
                </div>
                <div class="form-group">
                    <label>
                        <input type="radio" name="importMode" value="replace" checked> Replace existing data
                    </label>
                    <label style="margin-left: 20px;">
                        <input type="radio" name="importMode" value="merge"> Merge with existing data
                    </label>
                </div>
                <button id="importBtn" class="btn btn-primary">Import JSON</button>
            </div>
        </section>

        <!-- Success/Error Messages -->
        <div id="successMessage" class="success-message"></div>
        <div id="adminErrorMessage" class="error-message"></div>
    </div>

    <script src="admin.js"></script>
</body>
</html>
