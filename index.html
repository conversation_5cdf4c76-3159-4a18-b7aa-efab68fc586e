<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SVG Icon Library</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600&display=swap" rel="stylesheet">
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="header">
            <h1 class="app-title">SVG Icon Library</h1>
            <p class="app-subtitle" id="subtitle">Loading icons...</p>
            <div style="margin-top: 15px;">
                <a href="admin.html" style="color: white; text-decoration: none; background: rgba(255,255,255,0.2); padding: 8px 16px; border-radius: 20px; font-size: 14px; margin-right: 10px;">🔧 Admin Panel</a>
                <span id="stats-display" style="color: rgba(255,255,255,0.8); font-size: 14px;"></span>
            </div>
        </header>

        <!-- Controls Panel -->
        <div class="controls-panel">
            <div class="control-group">
                <label for="search-input">Search Icons:</label>
                <input type="text" id="search-input" placeholder="Search by name or category..." />
            </div>
            
            <div class="control-group">
                <label for="category-filter">Category:</label>
                <select id="category-filter">
                    <option value="all">All Categories</option>
                    <option value="navigation">Navigation</option>
                    <option value="actions">Actions</option>
                    <option value="communication">Communication</option>
                    <option value="media">Media</option>
                    <option value="files">Files</option>
                    <option value="ui">UI Elements</option>
                </select>
            </div>
            
            <div class="control-group">
                <label for="color-picker">Icon Color:</label>
                <input type="color" id="color-picker" value="#333333" />
                <div class="color-presets">
                    <button class="color-preset" data-color="#333333" style="background-color: #333333;"></button>
                    <button class="color-preset" data-color="#007bff" style="background-color: #007bff;"></button>
                    <button class="color-preset" data-color="#28a745" style="background-color: #28a745;"></button>
                    <button class="color-preset" data-color="#dc3545" style="background-color: #dc3545;"></button>
                    <button class="color-preset" data-color="#ffc107" style="background-color: #ffc107;"></button>
                    <button class="color-preset" data-color="#6f42c1" style="background-color: #6f42c1;"></button>
                </div>
            </div>
            
            <div class="control-group">
                <label for="size-slider">Icon Size: <span id="size-value">32px</span></label>
                <input type="range" id="size-slider" min="16" max="128" value="32" />
            </div>
        </div>

        <!-- Preview Area -->
        <div class="preview-area" id="preview-area" style="display: none;">
            <h3>Preview</h3>
            <div class="preview-content">
                <div class="preview-icon" id="preview-icon"></div>
                <div class="preview-info">
                    <h4 id="preview-name">Icon Name</h4>
                    <p id="preview-category">Category</p>
                    <div class="preview-sizes">
                        <div class="size-demo">
                            <span>16px</span>
                            <div class="size-icon size-16" id="preview-16"></div>
                        </div>
                        <div class="size-demo">
                            <span>24px</span>
                            <div class="size-icon size-24" id="preview-24"></div>
                        </div>
                        <div class="size-demo">
                            <span>32px</span>
                            <div class="size-icon size-32" id="preview-32"></div>
                        </div>
                        <div class="size-demo">
                            <span>48px</span>
                            <div class="size-icon size-48" id="preview-48"></div>
                        </div>
                        <div class="size-demo">
                            <span>64px</span>
                            <div class="size-icon size-64" id="preview-64"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Icons Grid -->
        <div class="icons-container">
            <div class="icons-grid" id="icons-grid">
                <!-- Icons will be dynamically generated here -->
            </div>
        </div>

        <!-- Notification -->
        <div class="notification" id="notification">
            <span id="notification-text">Copied to clipboard!</span>
        </div>
    </div>

    <div class="cookie-consent" id="cookieConsent">
        <p>This website uses cookies to ensure you get the best experience. <a href="https://jermesa.com/privacy-policy/">Learn more</a></p>
        <button id="acceptCookies">Accept</button>
    </div>

    <footer class="site-footer">
        <p>SVG Icon Library by <a href="https://www.jermesa.com" target="_blank">Jermesa Studio</a></p>
        <p><a href="https://jermesa.com/privacy-policy/" target="_blank">Privacy Policy</a></p>
                <div class="font-license">
            Font: Inter (https://fonts.google.com/specimen/Inter) - Licensed under Open Font License
        </div>
    </footer>

    <script src="app.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const cookieConsent = document.getElementById('cookieConsent');
            const acceptBtn = document.getElementById('acceptCookies');
            
            if (!localStorage.getItem('cookiesAccepted')) {
                cookieConsent.style.display = 'block';
            }
            
            acceptBtn.addEventListener('click', function() {
                localStorage.setItem('cookiesAccepted', 'true');
                cookieConsent.style.display = 'none';
            });
        });
    </script>
</body>
</html>
