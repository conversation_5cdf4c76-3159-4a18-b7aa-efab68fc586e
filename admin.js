// Admin Panel JavaScript
class AdminPanel {
    constructor() {
        this.iconsData = [];
        this.categories = new Set();
        this.adminPassword = 'admin123'; // In production, this should be more secure
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.checkAuthStatus();
    }

    setupEventListeners() {
        // Login form
        document.getElementById('adminLoginForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleLogin();
        });

        // Logout button
        document.getElementById('logoutBtn').addEventListener('click', () => {
            this.handleLogout();
        });

        // Navigation
        document.querySelectorAll('.nav-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchSection(e.target.dataset.section);
            });
        });

        // Add icon form
        document.getElementById('addIconForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.addIcon();
        });

        // Add category form
        document.getElementById('addCategoryForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.addCategory();
        });

        // File upload
        const fileUpload = document.getElementById('fileUpload');
        const fileInput = document.getElementById('fileInput');
        
        fileUpload.addEventListener('click', () => fileInput.click());
        fileUpload.addEventListener('dragover', (e) => {
            e.preventDefault();
            fileUpload.classList.add('dragover');
        });
        fileUpload.addEventListener('dragleave', () => {
            fileUpload.classList.remove('dragover');
        });
        fileUpload.addEventListener('drop', (e) => {
            e.preventDefault();
            fileUpload.classList.remove('dragover');
            this.handleFileUpload(e.dataTransfer.files);
        });
        fileInput.addEventListener('change', (e) => {
            this.handleFileUpload(e.target.files);
        });

        // Export/Import buttons
        document.getElementById('exportBtn').addEventListener('click', () => {
            this.exportData();
        });
        document.getElementById('importBtn').addEventListener('click', () => {
            this.importData();
        });
    }

    checkAuthStatus() {
        const isAuthenticated = sessionStorage.getItem('adminAuthenticated');
        if (isAuthenticated === 'true') {
            this.showAdminPanel();
        }
    }

    handleLogin() {
        const password = document.getElementById('adminPassword').value;
        const errorDiv = document.getElementById('errorMessage');

        if (password === this.adminPassword) {
            sessionStorage.setItem('adminAuthenticated', 'true');
            this.showAdminPanel();
            errorDiv.style.display = 'none';
        } else {
            errorDiv.textContent = 'Invalid password. Please try again.';
            errorDiv.style.display = 'block';
        }
    }

    handleLogout() {
        sessionStorage.removeItem('adminAuthenticated');
        document.getElementById('loginForm').style.display = 'block';
        document.getElementById('adminPanel').style.display = 'none';
    }

    async showAdminPanel() {
        document.getElementById('loginForm').style.display = 'none';
        document.getElementById('adminPanel').style.display = 'block';
        
        // Load data
        await this.loadIconsData();
        this.updateCategoriesDropdown();
        this.renderIcons();
        this.renderCategories();
    }

    switchSection(sectionName) {
        // Update navigation
        document.querySelectorAll('.nav-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-section="${sectionName}"]`).classList.add('active');

        // Update sections
        document.querySelectorAll('.admin-section').forEach(section => {
            section.classList.remove('active');
        });
        document.getElementById(`${sectionName}-section`).classList.add('active');
    }

    async loadIconsData() {
        try {
            const response = await fetch('icons-data.json');
            if (!response.ok) {
                throw new Error('Failed to load icons data');
            }
            this.iconsData = await response.json();
            
            // Extract categories
            this.categories.clear();
            this.iconsData.forEach(icon => {
                this.categories.add(icon.category);
            });
            
            return this.iconsData;
        } catch (error) {
            console.error('Error loading icons data:', error);
            this.showError('Failed to load icons data');
            return [];
        }
    }

    updateCategoriesDropdown() {
        const select = document.getElementById('iconCategory');
        select.innerHTML = '<option value="">Select Category</option>';
        
        Array.from(this.categories).sort().forEach(category => {
            const option = document.createElement('option');
            option.value = category;
            option.textContent = category.charAt(0).toUpperCase() + category.slice(1);
            select.appendChild(option);
        });
    }

    renderIcons() {
        const container = document.getElementById('iconsList');
        container.innerHTML = '';

        this.iconsData.forEach(icon => {
            const iconCard = document.createElement('div');
            iconCard.className = 'icon-card';
            iconCard.innerHTML = `
                <div class="icon-preview">${icon.svg}</div>
                <div class="icon-info">
                    <h4>${icon.name}</h4>
                    <p>${icon.category}</p>
                </div>
                <div class="icon-actions">
                    <button class="btn btn-primary" onclick="adminPanel.editIcon('${icon.id}')">Edit</button>
                    <button class="btn btn-danger" onclick="adminPanel.deleteIcon('${icon.id}')">Delete</button>
                </div>
            `;
            container.appendChild(iconCard);
        });
    }

    renderCategories() {
        const container = document.getElementById('categoriesList');
        container.innerHTML = '';

        Array.from(this.categories).sort().forEach(category => {
            const categoryTag = document.createElement('div');
            categoryTag.className = 'category-tag';
            categoryTag.innerHTML = `
                ${category.charAt(0).toUpperCase() + category.slice(1)}
                <button class="remove-btn" onclick="adminPanel.deleteCategory('${category}')" title="Remove category">×</button>
            `;
            container.appendChild(categoryTag);
        });
    }

    addIcon() {
        const id = document.getElementById('iconId').value.trim();
        const name = document.getElementById('iconName').value.trim();
        const category = document.getElementById('iconCategory').value;
        const svg = document.getElementById('iconSvg').value.trim();

        // Validation
        if (!id || !name || !category || !svg) {
            this.showError('Please fill in all fields');
            return;
        }

        // Check if ID already exists
        if (this.iconsData.find(icon => icon.id === id)) {
            this.showError('Icon ID already exists. Please choose a different ID.');
            return;
        }

        // Validate SVG
        if (!svg.includes('<svg') || !svg.includes('</svg>')) {
            this.showError('Please provide valid SVG code');
            return;
        }

        // Add icon
        const newIcon = { id, name, category, svg };
        this.iconsData.push(newIcon);
        this.categories.add(category);

        // Save and update UI
        this.saveIconsData();
        this.updateCategoriesDropdown();
        this.renderIcons();
        this.renderCategories();

        // Clear form
        document.getElementById('addIconForm').reset();
        this.showSuccess('Icon added successfully!');
    }

    editIcon(iconId) {
        const icon = this.iconsData.find(i => i.id === iconId);
        if (!icon) return;

        // Pre-fill form with existing data
        document.getElementById('iconId').value = icon.id;
        document.getElementById('iconName').value = icon.name;
        document.getElementById('iconCategory').value = icon.category;
        document.getElementById('iconSvg').value = icon.svg;

        // Remove the existing icon (will be re-added when form is submitted)
        this.deleteIcon(iconId, false);
        
        // Scroll to form
        document.getElementById('addIconForm').scrollIntoView({ behavior: 'smooth' });
    }

    deleteIcon(iconId, showConfirm = true) {
        if (showConfirm && !confirm('Are you sure you want to delete this icon?')) {
            return;
        }

        this.iconsData = this.iconsData.filter(icon => icon.id !== iconId);
        this.saveIconsData();
        this.renderIcons();
        
        if (showConfirm) {
            this.showSuccess('Icon deleted successfully!');
        }
    }

    addCategory() {
        const categoryName = document.getElementById('categoryName').value.trim().toLowerCase();
        
        if (!categoryName) {
            this.showError('Please enter a category name');
            return;
        }

        if (this.categories.has(categoryName)) {
            this.showError('Category already exists');
            return;
        }

        this.categories.add(categoryName);
        this.updateCategoriesDropdown();
        this.renderCategories();
        document.getElementById('addCategoryForm').reset();
        this.showSuccess('Category added successfully!');
    }

    deleteCategory(categoryName) {
        if (!confirm(`Are you sure you want to delete the "${categoryName}" category? This will also delete all icons in this category.`)) {
            return;
        }

        // Remove icons in this category
        this.iconsData = this.iconsData.filter(icon => icon.category !== categoryName);
        this.categories.delete(categoryName);

        this.saveIconsData();
        this.updateCategoriesDropdown();
        this.renderIcons();
        this.renderCategories();
        this.showSuccess('Category and associated icons deleted successfully!');
    }

    async handleFileUpload(files) {
        const svgFiles = Array.from(files).filter(file => file.name.endsWith('.svg'));
        
        if (svgFiles.length === 0) {
            this.showError('Please select SVG files only');
            return;
        }

        for (const file of svgFiles) {
            try {
                const svgContent = await this.readFileAsText(file);
                const fileName = file.name.replace('.svg', '');
                const iconId = fileName.toLowerCase().replace(/[^a-z0-9]/g, '-');
                const iconName = fileName.replace(/[-_]/g, ' ').replace(/\b\w/g, l => l.toUpperCase());

                // Check if ID already exists
                if (this.iconsData.find(icon => icon.id === iconId)) {
                    console.warn(`Icon with ID "${iconId}" already exists, skipping...`);
                    continue;
                }

                const newIcon = {
                    id: iconId,
                    name: iconName,
                    category: 'ui', // Default category for uploaded files
                    svg: svgContent
                };

                this.iconsData.push(newIcon);
                this.categories.add('ui');
            } catch (error) {
                console.error(`Error processing file ${file.name}:`, error);
            }
        }

        this.saveIconsData();
        this.updateCategoriesDropdown();
        this.renderIcons();
        this.renderCategories();
        this.showSuccess(`${svgFiles.length} SVG files uploaded successfully!`);
    }

    readFileAsText(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = e => resolve(e.target.result);
            reader.onerror = reject;
            reader.readAsText(file);
        });
    }

    exportData() {
        const dataStr = JSON.stringify(this.iconsData, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        const url = URL.createObjectURL(dataBlob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `icons-data-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        this.showSuccess('Icon library exported successfully!');
    }

    async importData() {
        const fileInput = document.getElementById('importFile');
        const importMode = document.querySelector('input[name="importMode"]:checked').value;
        
        if (!fileInput.files[0]) {
            this.showError('Please select a JSON file to import');
            return;
        }

        try {
            const fileContent = await this.readFileAsText(fileInput.files[0]);
            const importedData = JSON.parse(fileContent);
            
            if (!Array.isArray(importedData)) {
                throw new Error('Invalid JSON format. Expected an array of icons.');
            }

            if (importMode === 'replace') {
                this.iconsData = importedData;
            } else {
                // Merge mode - avoid duplicates
                importedData.forEach(importedIcon => {
                    const existingIndex = this.iconsData.findIndex(icon => icon.id === importedIcon.id);
                    if (existingIndex >= 0) {
                        this.iconsData[existingIndex] = importedIcon; // Update existing
                    } else {
                        this.iconsData.push(importedIcon); // Add new
                    }
                });
            }

            // Update categories
            this.categories.clear();
            this.iconsData.forEach(icon => {
                this.categories.add(icon.category);
            });

            this.saveIconsData();
            this.updateCategoriesDropdown();
            this.renderIcons();
            this.renderCategories();
            this.showSuccess(`Icon library imported successfully! (${importedData.length} icons processed)`);
            
        } catch (error) {
            console.error('Import error:', error);
            this.showError('Failed to import data. Please check the JSON file format.');
        }
    }

    async saveIconsData() {
        // In a real application, this would make an API call to save data
        // For now, we'll simulate saving by updating localStorage
        localStorage.setItem('iconsData', JSON.stringify(this.iconsData));
        
        // Also update the JSON file (this would require server-side implementation)
        console.log('Icons data saved:', this.iconsData);
    }

    showSuccess(message) {
        const successDiv = document.getElementById('successMessage');
        successDiv.textContent = message;
        successDiv.style.display = 'block';
        setTimeout(() => {
            successDiv.style.display = 'none';
        }, 5000);
    }

    showError(message) {
        const errorDiv = document.getElementById('adminErrorMessage');
        errorDiv.textContent = message;
        errorDiv.style.display = 'block';
        setTimeout(() => {
            errorDiv.style.display = 'none';
        }, 5000);
    }
}

// Initialize admin panel
document.addEventListener('DOMContentLoaded', () => {
    window.adminPanel = new AdminPanel();
});
