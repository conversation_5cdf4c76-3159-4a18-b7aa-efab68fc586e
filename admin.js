// Admin Panel JavaScript
class AdminPanel {
    constructor() {
        this.iconsData = [];
        this.categories = new Set();
        this.adminPassword = 'admin123'; // In production, this should be more secure
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.checkAuthStatus();
    }

    setupEventListeners() {
        // Login form
        document.getElementById('adminLoginForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleLogin();
        });

        // Logout button
        document.getElementById('logoutBtn').addEventListener('click', () => {
            this.handleLogout();
        });

        // Navigation
        document.querySelectorAll('.nav-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchSection(e.target.dataset.section);
            });
        });

        // Add icon form
        document.getElementById('addIconForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.addIcon();
        });

        // Add category form
        document.getElementById('addCategoryForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.addCategory();
        });

        // Reassign category form
        document.getElementById('reassignCategoryForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.reassignCategory();
        });

        // File upload
        const fileUpload = document.getElementById('fileUpload');
        const fileInput = document.getElementById('fileInput');
        
        fileUpload.addEventListener('click', () => fileInput.click());
        fileUpload.addEventListener('dragover', (e) => {
            e.preventDefault();
            fileUpload.classList.add('dragover');
        });
        fileUpload.addEventListener('dragleave', () => {
            fileUpload.classList.remove('dragover');
        });
        fileUpload.addEventListener('drop', (e) => {
            e.preventDefault();
            fileUpload.classList.remove('dragover');
            this.handleFileUpload(e.dataTransfer.files);
        });
        fileInput.addEventListener('change', (e) => {
            this.handleFileUpload(e.target.files);
        });

        // Export/Import buttons
        document.getElementById('exportBtn').addEventListener('click', () => {
            this.exportData();
        });
        document.getElementById('exportCsvBtn').addEventListener('click', () => {
            this.exportCSV();
        });
        document.getElementById('exportBackupBtn').addEventListener('click', () => {
            this.createBackup();
        });
        document.getElementById('importBtn').addEventListener('click', () => {
            this.importData();
        });

        // Preview button
        document.getElementById('previewBtn').addEventListener('click', () => {
            this.previewCurrentSVG();
        });

        // Auto-preview on SVG input change
        document.getElementById('iconSvg').addEventListener('input', (e) => {
            if (e.target.value.trim()) {
                this.previewCurrentSVG();
            }
        });

        // Auto-generate ID from name
        document.getElementById('iconName').addEventListener('input', (e) => {
            const nameInput = e.target;
            const idInput = document.getElementById('iconId');

            // Only auto-generate if ID field is empty or was auto-generated
            if (!idInput.dataset.userModified) {
                idInput.value = this.generateIconId(nameInput.value);
            }
        });

        // Track if user manually modified the ID
        document.getElementById('iconId').addEventListener('input', (e) => {
            e.target.dataset.userModified = 'true';
        });
    }

    checkAuthStatus() {
        const isAuthenticated = sessionStorage.getItem('adminAuthenticated');
        if (isAuthenticated === 'true') {
            this.showAdminPanel();
        }
    }

    handleLogin() {
        const password = document.getElementById('adminPassword').value;
        const errorDiv = document.getElementById('errorMessage');

        if (password === this.adminPassword) {
            sessionStorage.setItem('adminAuthenticated', 'true');
            this.showAdminPanel();
            errorDiv.style.display = 'none';
        } else {
            errorDiv.textContent = 'Invalid password. Please try again.';
            errorDiv.style.display = 'block';
        }
    }

    handleLogout() {
        sessionStorage.removeItem('adminAuthenticated');
        document.getElementById('loginForm').style.display = 'block';
        document.getElementById('adminPanel').style.display = 'none';
    }

    async showAdminPanel() {
        document.getElementById('loginForm').style.display = 'none';
        document.getElementById('adminPanel').style.display = 'block';
        
        // Load data
        await this.loadIconsData();
        this.updateCategoriesDropdown();
        this.renderIcons();
        this.renderCategories();
        this.updateStatistics();
    }

    switchSection(sectionName) {
        // Update navigation
        document.querySelectorAll('.nav-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-section="${sectionName}"]`).classList.add('active');

        // Update sections
        document.querySelectorAll('.admin-section').forEach(section => {
            section.classList.remove('active');
        });
        document.getElementById(`${sectionName}-section`).classList.add('active');

        // Update breadcrumb
        this.updateBreadcrumb(sectionName);
    }

    updateBreadcrumb(sectionName) {
        const currentSection = document.getElementById('currentSection');
        if (currentSection) {
            const sectionNames = {
                'icons': 'Manage Icons',
                'categories': 'Manage Categories',
                'import-export': 'Import/Export'
            };
            currentSection.textContent = sectionNames[sectionName] || sectionName;
        }
    }

    updateStatistics() {
        const statsDiv = document.getElementById('adminStats');
        if (statsDiv && this.iconsData) {
            const totalIcons = this.iconsData.length;
            const totalCategories = this.categories.size;

            // Calculate category breakdown
            const categoryStats = {};
            this.iconsData.forEach(icon => {
                categoryStats[icon.category] = (categoryStats[icon.category] || 0) + 1;
            });

            const topCategories = Object.entries(categoryStats)
                .sort(([,a], [,b]) => b - a)
                .slice(0, 3)
                .map(([cat, count]) => `${cat} (${count})`)
                .join(', ');

            statsDiv.innerHTML = `
                <div style="display: flex; gap: 30px; flex-wrap: wrap; align-items: center;">
                    <div><strong>${totalIcons}</strong> Total Icons</div>
                    <div><strong>${totalCategories}</strong> Categories</div>
                    <div style="flex: 1; min-width: 200px;">
                        <strong>Top Categories:</strong> ${topCategories}
                    </div>
                    <div style="font-size: 12px; opacity: 0.8;">
                        Last updated: ${new Date().toLocaleString()}
                    </div>
                </div>
            `;
        }
    }

    async loadIconsData() {
        try {
            const response = await fetch('icons-data.json');
            if (!response.ok) {
                throw new Error('Failed to load icons data');
            }
            this.iconsData = await response.json();
            
            // Extract categories
            this.categories.clear();
            this.iconsData.forEach(icon => {
                this.categories.add(icon.category);
            });
            
            return this.iconsData;
        } catch (error) {
            console.error('Error loading icons data:', error);
            this.showError('Failed to load icons data');
            return [];
        }
    }

    updateCategoriesDropdown() {
        // Update main icon category dropdown
        const select = document.getElementById('iconCategory');
        select.innerHTML = '<option value="">Select Category</option>';

        Array.from(this.categories).sort().forEach(category => {
            const option = document.createElement('option');
            option.value = category;
            option.textContent = category.charAt(0).toUpperCase() + category.slice(1);
            select.appendChild(option);
        });

        // Update reassignment dropdowns
        this.updateReassignmentDropdowns();
    }

    updateReassignmentDropdowns() {
        const fromSelect = document.getElementById('fromCategory');
        const toSelect = document.getElementById('toCategory');

        if (fromSelect && toSelect) {
            fromSelect.innerHTML = '<option value="">Select source category</option>';
            toSelect.innerHTML = '<option value="">Select target category</option>';

            Array.from(this.categories).sort().forEach(category => {
                const fromOption = document.createElement('option');
                fromOption.value = category;
                fromOption.textContent = `${category.charAt(0).toUpperCase() + category.slice(1)} (${this.getIconCountByCategory(category)} icons)`;
                fromSelect.appendChild(fromOption);

                const toOption = document.createElement('option');
                toOption.value = category;
                toOption.textContent = category.charAt(0).toUpperCase() + category.slice(1);
                toSelect.appendChild(toOption);
            });
        }
    }

    getIconCountByCategory(category) {
        return this.iconsData.filter(icon => icon.category === category).length;
    }

    renderIcons() {
        const container = document.getElementById('iconsList');
        container.innerHTML = '';

        this.iconsData.forEach(icon => {
            const iconCard = document.createElement('div');
            iconCard.className = 'icon-card';
            iconCard.innerHTML = `
                <div class="icon-preview">${icon.svg}</div>
                <div class="icon-info">
                    <h4>${icon.name}</h4>
                    <p>${icon.category}</p>
                </div>
                <div class="icon-actions">
                    <button class="btn btn-primary" onclick="adminPanel.editIcon('${icon.id}')">Edit</button>
                    <button class="btn btn-danger" onclick="adminPanel.deleteIcon('${icon.id}')">Delete</button>
                </div>
            `;
            container.appendChild(iconCard);
        });
    }

    renderCategories() {
        const container = document.getElementById('categoriesList');
        container.innerHTML = '';

        Array.from(this.categories).sort().forEach(category => {
            const categoryTag = document.createElement('div');
            categoryTag.className = 'category-tag';
            categoryTag.innerHTML = `
                ${category.charAt(0).toUpperCase() + category.slice(1)}
                <button class="remove-btn" onclick="adminPanel.deleteCategory('${category}')" title="Remove category">×</button>
            `;
            container.appendChild(categoryTag);
        });
    }

    addIcon() {
        const id = document.getElementById('iconId').value.trim();
        const name = document.getElementById('iconName').value.trim();
        const category = document.getElementById('iconCategory').value;
        const svg = document.getElementById('iconSvg').value.trim();

        // Validation
        if (!id || !name || !category || !svg) {
            this.showError('Please fill in all fields');
            return;
        }

        // Check if ID already exists
        if (this.iconsData.find(icon => icon.id === id)) {
            this.showError('Icon ID already exists. Please choose a different ID.');
            return;
        }

        // Validate SVG
        if (!this.isValidSVG(svg)) {
            this.showError('Please provide valid SVG code');
            return;
        }

        // Clean and normalize SVG
        const cleanSvg = this.cleanSVG(svg);

        // Add icon
        const newIcon = { id, name, category, svg: cleanSvg };
        this.iconsData.push(newIcon);
        this.categories.add(category);

        // Save and update UI
        this.saveIconsData();
        this.updateCategoriesDropdown();
        this.renderIcons();
        this.renderCategories();
        this.updateStatistics();

        // Clear form
        document.getElementById('addIconForm').reset();
        this.showSuccess('Icon added successfully!');
    }

    editIcon(iconId) {
        const icon = this.iconsData.find(i => i.id === iconId);
        if (!icon) return;

        // Pre-fill form with existing data
        document.getElementById('iconId').value = icon.id;
        document.getElementById('iconName').value = icon.name;
        document.getElementById('iconCategory').value = icon.category;
        document.getElementById('iconSvg').value = icon.svg;

        // Remove the existing icon (will be re-added when form is submitted)
        this.deleteIcon(iconId, false);
        
        // Scroll to form
        document.getElementById('addIconForm').scrollIntoView({ behavior: 'smooth' });
    }

    deleteIcon(iconId, showConfirm = true) {
        if (showConfirm && !confirm('Are you sure you want to delete this icon?')) {
            return;
        }

        this.iconsData = this.iconsData.filter(icon => icon.id !== iconId);
        this.saveIconsData();
        this.renderIcons();
        this.updateStatistics();

        if (showConfirm) {
            this.showSuccess('Icon deleted successfully!');
        }
    }

    addCategory() {
        const categoryName = document.getElementById('categoryName').value.trim().toLowerCase();
        
        if (!categoryName) {
            this.showError('Please enter a category name');
            return;
        }

        if (this.categories.has(categoryName)) {
            this.showError('Category already exists');
            return;
        }

        this.categories.add(categoryName);
        this.updateCategoriesDropdown();
        this.renderCategories();
        document.getElementById('addCategoryForm').reset();
        this.showSuccess('Category added successfully!');
    }

    deleteCategory(categoryName) {
        const iconCount = this.getIconCountByCategory(categoryName);

        if (!confirm(`Are you sure you want to delete the "${categoryName}" category? This will also delete ${iconCount} icons in this category.`)) {
            return;
        }

        // Remove icons in this category
        this.iconsData = this.iconsData.filter(icon => icon.category !== categoryName);
        this.categories.delete(categoryName);

        this.saveIconsData();
        this.updateCategoriesDropdown();
        this.renderIcons();
        this.renderCategories();
        this.showSuccess(`Category "${categoryName}" and ${iconCount} associated icons deleted successfully!`);
    }

    reassignCategory() {
        const fromCategory = document.getElementById('fromCategory').value;
        const toCategory = document.getElementById('toCategory').value;

        if (!fromCategory || !toCategory) {
            this.showError('Please select both source and target categories');
            return;
        }

        if (fromCategory === toCategory) {
            this.showError('Source and target categories cannot be the same');
            return;
        }

        const iconsToReassign = this.iconsData.filter(icon => icon.category === fromCategory);

        if (iconsToReassign.length === 0) {
            this.showError(`No icons found in category "${fromCategory}"`);
            return;
        }

        if (!confirm(`Are you sure you want to reassign ${iconsToReassign.length} icons from "${fromCategory}" to "${toCategory}"?`)) {
            return;
        }

        // Reassign icons
        this.iconsData.forEach(icon => {
            if (icon.category === fromCategory) {
                icon.category = toCategory;
            }
        });

        // Remove empty category
        this.categories.delete(fromCategory);

        this.saveIconsData();
        this.updateCategoriesDropdown();
        this.renderIcons();
        this.renderCategories();

        // Clear form
        document.getElementById('reassignCategoryForm').reset();

        this.showSuccess(`Successfully reassigned ${iconsToReassign.length} icons from "${fromCategory}" to "${toCategory}"`);
    }

    async handleFileUpload(files) {
        const svgFiles = Array.from(files).filter(file => file.name.endsWith('.svg'));

        if (svgFiles.length === 0) {
            this.showError('Please select SVG files only');
            return;
        }

        let successCount = 0;
        let skipCount = 0;
        let errorCount = 0;

        for (const file of svgFiles) {
            try {
                const svgContent = await this.readFileAsText(file);

                // Validate SVG content
                if (!this.isValidSVG(svgContent)) {
                    console.warn(`Invalid SVG file: ${file.name}`);
                    errorCount++;
                    continue;
                }

                const fileName = file.name.replace('.svg', '');
                const iconId = this.generateIconId(fileName);
                const iconName = fileName.replace(/[-_]/g, ' ').replace(/\b\w/g, l => l.toUpperCase());

                // Check if ID already exists
                if (this.iconsData.find(icon => icon.id === iconId)) {
                    console.warn(`Icon with ID "${iconId}" already exists, skipping...`);
                    skipCount++;
                    continue;
                }

                const cleanSvg = this.cleanSVG(svgContent);
                const newIcon = {
                    id: iconId,
                    name: iconName,
                    category: 'ui', // Default category for uploaded files
                    svg: cleanSvg
                };

                this.iconsData.push(newIcon);
                this.categories.add('ui');
                successCount++;
            } catch (error) {
                console.error(`Error processing file ${file.name}:`, error);
                errorCount++;
            }
        }

        if (successCount > 0) {
            this.saveIconsData();
            this.updateCategoriesDropdown();
            this.renderIcons();
            this.renderCategories();
        }

        // Show detailed feedback
        let message = `Upload complete: ${successCount} icons added`;
        if (skipCount > 0) message += `, ${skipCount} skipped (duplicates)`;
        if (errorCount > 0) message += `, ${errorCount} failed (invalid)`;

        if (successCount > 0) {
            this.showSuccess(message);
        } else {
            this.showError(message);
        }
    }

    readFileAsText(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = e => resolve(e.target.result);
            reader.onerror = reject;
            reader.readAsText(file);
        });
    }

    exportData() {
        const dataStr = JSON.stringify(this.iconsData, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        const url = URL.createObjectURL(dataBlob);

        const a = document.createElement('a');
        a.href = url;
        a.download = `icons-data-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        this.showSuccess('Icon library exported as JSON successfully!');
    }

    exportCSV() {
        // Create CSV header
        const headers = ['ID', 'Name', 'Category', 'SVG Code'];
        const csvRows = [headers.join(',')];

        // Add data rows
        this.iconsData.forEach(icon => {
            const row = [
                `"${icon.id}"`,
                `"${icon.name}"`,
                `"${icon.category}"`,
                `"${icon.svg.replace(/"/g, '""')}"` // Escape quotes in SVG
            ];
            csvRows.push(row.join(','));
        });

        const csvContent = csvRows.join('\n');
        const dataBlob = new Blob([csvContent], { type: 'text/csv' });
        const url = URL.createObjectURL(dataBlob);

        const a = document.createElement('a');
        a.href = url;
        a.download = `icons-data-${new Date().toISOString().split('T')[0]}.csv`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        this.showSuccess('Icon library exported as CSV successfully!');
    }

    createBackup() {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const backupData = {
            timestamp: new Date().toISOString(),
            version: '1.0',
            iconCount: this.iconsData.length,
            categories: Array.from(this.categories),
            icons: this.iconsData
        };

        const dataStr = JSON.stringify(backupData, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        const url = URL.createObjectURL(dataBlob);

        const a = document.createElement('a');
        a.href = url;
        a.download = `icons-backup-${timestamp}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        this.showSuccess(`Backup created successfully! (${this.iconsData.length} icons)`);
    }

    async importData() {
        const fileInput = document.getElementById('importFile');
        const importMode = document.querySelector('input[name="importMode"]:checked').value;

        if (!fileInput.files[0]) {
            this.showError('Please select a JSON file to import');
            return;
        }

        try {
            const fileContent = await this.readFileAsText(fileInput.files[0]);
            const parsedData = JSON.parse(fileContent);

            let importedData;
            let isBackupFile = false;

            // Check if it's a backup file format
            if (parsedData.icons && parsedData.timestamp && parsedData.version) {
                importedData = parsedData.icons;
                isBackupFile = true;
                console.log('Detected backup file format');
            } else if (Array.isArray(parsedData)) {
                importedData = parsedData;
            } else {
                throw new Error('Invalid JSON format. Expected an array of icons or a backup file.');
            }

            // Validate imported data
            const validationResult = this.validateIconsData(importedData);
            if (!validationResult.valid) {
                throw new Error(`Data validation failed: ${validationResult.errors.join(', ')}`);
            }

            const originalCount = this.iconsData.length;

            if (importMode === 'replace') {
                this.iconsData = importedData;
            } else {
                // Merge mode - avoid duplicates
                let addedCount = 0;
                let updatedCount = 0;

                importedData.forEach(importedIcon => {
                    const existingIndex = this.iconsData.findIndex(icon => icon.id === importedIcon.id);
                    if (existingIndex >= 0) {
                        this.iconsData[existingIndex] = importedIcon; // Update existing
                        updatedCount++;
                    } else {
                        this.iconsData.push(importedIcon); // Add new
                        addedCount++;
                    }
                });

                console.log(`Merge complete: ${addedCount} added, ${updatedCount} updated`);
            }

            // Update categories
            this.categories.clear();
            this.iconsData.forEach(icon => {
                this.categories.add(icon.category);
            });

            this.saveIconsData();
            this.updateCategoriesDropdown();
            this.renderIcons();
            this.renderCategories();

            const message = isBackupFile
                ? `Backup file imported successfully! (${importedData.length} icons from ${new Date(parsedData.timestamp).toLocaleDateString()})`
                : `Icon library imported successfully! (${importedData.length} icons processed)`;

            this.showSuccess(message);

        } catch (error) {
            console.error('Import error:', error);
            this.showError(`Failed to import data: ${error.message}`);
        }
    }

    validateIconsData(data) {
        const errors = [];

        if (!Array.isArray(data)) {
            return { valid: false, errors: ['Data must be an array'] };
        }

        data.forEach((icon, index) => {
            const requiredFields = ['id', 'name', 'category', 'svg'];
            requiredFields.forEach(field => {
                if (!icon[field] || typeof icon[field] !== 'string') {
                    errors.push(`Icon ${index + 1}: Missing or invalid ${field}`);
                }
            });

            // Validate SVG
            if (icon.svg && !this.isValidSVG(icon.svg)) {
                errors.push(`Icon ${index + 1}: Invalid SVG format`);
            }
        });

        return {
            valid: errors.length === 0,
            errors: errors
        };
    }

    async saveIconsData() {
        try {
            // Save to localStorage as backup
            localStorage.setItem('iconsData', JSON.stringify(this.iconsData));

            // Try to save to server via API
            try {
                const response = await fetch('/api/save-icons', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(this.iconsData)
                });

                if (response.ok) {
                    const result = await response.json();
                    console.log('Icons data saved to server:', result);
                    this.showSuccess(`Successfully saved ${result.count} icons to server!`);
                    return;
                }
            } catch (serverError) {
                console.warn('Server save failed, falling back to download option:', serverError);
            }

            // Fallback: create downloadable file
            const dataStr = JSON.stringify(this.iconsData, null, 2);
            const blob = new Blob([dataStr], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            this.lastSavedDataUrl = url;

            console.log('Icons data prepared for download:', this.iconsData.length, 'icons');
            this.showSaveOption();

        } catch (error) {
            console.error('Error saving icons data:', error);
            this.showError('Failed to save data');
        }
    }

    showSaveOption() {
        const existingBtn = document.getElementById('downloadUpdatedData');
        if (existingBtn) {
            existingBtn.remove();
        }

        const downloadBtn = document.createElement('button');
        downloadBtn.id = 'downloadUpdatedData';
        downloadBtn.className = 'btn btn-success';
        downloadBtn.textContent = 'Download Updated icons-data.json';
        downloadBtn.style.position = 'fixed';
        downloadBtn.style.top = '20px';
        downloadBtn.style.right = '20px';
        downloadBtn.style.zIndex = '1000';

        downloadBtn.addEventListener('click', () => {
            const a = document.createElement('a');
            a.href = this.lastSavedDataUrl;
            a.download = 'icons-data.json';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            downloadBtn.remove();
            this.showSuccess('Updated icons-data.json downloaded! Replace the existing file to apply changes.');
        });

        document.body.appendChild(downloadBtn);

        // Auto-remove after 30 seconds
        setTimeout(() => {
            if (downloadBtn.parentNode) {
                downloadBtn.remove();
            }
        }, 30000);
    }

    showSuccess(message) {
        const successDiv = document.getElementById('successMessage');
        successDiv.textContent = message;
        successDiv.style.display = 'block';
        setTimeout(() => {
            successDiv.style.display = 'none';
        }, 5000);
    }

    showError(message) {
        const errorDiv = document.getElementById('adminErrorMessage');
        errorDiv.textContent = message;
        errorDiv.style.display = 'block';
        setTimeout(() => {
            errorDiv.style.display = 'none';
        }, 5000);
    }

    isValidSVG(svgString) {
        // Basic SVG validation
        const trimmed = svgString.trim();
        return trimmed.includes('<svg') &&
               trimmed.includes('</svg>') &&
               trimmed.indexOf('<svg') < trimmed.lastIndexOf('</svg>');
    }

    cleanSVG(svgString) {
        // Clean and normalize SVG
        let cleaned = svgString.trim();

        // Ensure proper SVG attributes for icon usage
        if (!cleaned.includes('viewBox=')) {
            // Try to extract width/height and convert to viewBox
            const widthMatch = cleaned.match(/width="([^"]+)"/);
            const heightMatch = cleaned.match(/height="([^"]+)"/);

            if (widthMatch && heightMatch) {
                const width = widthMatch[1];
                const height = heightMatch[1];
                cleaned = cleaned.replace('<svg', `<svg viewBox="0 0 ${width} ${height}"`);
            } else {
                // Default viewBox
                cleaned = cleaned.replace('<svg', '<svg viewBox="0 0 24 24"');
            }
        }

        // Ensure consistent stroke attributes for theming
        if (!cleaned.includes('stroke="currentColor"')) {
            cleaned = cleaned.replace('<svg', '<svg fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"');
        }

        return cleaned;
    }

    generateIconId(name) {
        // Generate a URL-friendly ID from the icon name
        return name.toLowerCase()
                  .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
                  .replace(/\s+/g, '-') // Replace spaces with hyphens
                  .replace(/-+/g, '-') // Replace multiple hyphens with single
                  .replace(/^-|-$/g, ''); // Remove leading/trailing hyphens
    }

    previewCurrentSVG() {
        const svgInput = document.getElementById('iconSvg');
        const svgString = svgInput.value.trim();

        if (!svgString) {
            this.hidePreview();
            return;
        }

        if (this.isValidSVG(svgString)) {
            this.previewSVG(svgString);
        } else {
            this.showPreviewError('Invalid SVG code');
        }
    }

    previewSVG(svgString) {
        const previewContainer = document.getElementById('svgPreview');
        if (previewContainer) {
            const cleanSvg = this.cleanSVG(svgString);
            previewContainer.innerHTML = `
                <div style="display: flex; align-items: center; justify-content: center; gap: 20px;">
                    <div style="width: 24px; height: 24px; color: #333;">${cleanSvg}</div>
                    <div style="width: 32px; height: 32px; color: #007bff;">${cleanSvg}</div>
                    <div style="width: 48px; height: 48px; color: #28a745;">${cleanSvg}</div>
                </div>
                <p style="margin: 10px 0 0; color: #6c757d; font-size: 12px;">Preview at different sizes and colors</p>
            `;
            previewContainer.style.display = 'block';
            previewContainer.style.border = '2px solid #28a745';
            previewContainer.style.backgroundColor = '#f8fff9';
        }
    }

    showPreviewError(message) {
        const previewContainer = document.getElementById('svgPreview');
        if (previewContainer) {
            previewContainer.innerHTML = `<p style="margin: 0; color: #dc3545;">${message}</p>`;
            previewContainer.style.display = 'block';
            previewContainer.style.border = '2px solid #dc3545';
            previewContainer.style.backgroundColor = '#fff5f5';
        }
    }

    hidePreview() {
        const previewContainer = document.getElementById('svgPreview');
        if (previewContainer) {
            previewContainer.style.display = 'none';
        }
    }
}

// Initialize admin panel
document.addEventListener('DOMContentLoaded', () => {
    window.adminPanel = new AdminPanel();
});
